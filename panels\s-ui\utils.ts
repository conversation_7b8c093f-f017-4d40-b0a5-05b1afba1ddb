import { AuthInfo, ConfigFormData, ServerStatus, SUIConfig } from '@/lib/types';
import { SSLPinningManager } from '@/lib/utils';
import { useAppStore } from '@/lib/store';

/**
 * S-UI面板API验证接口响应类型
 */
interface SUILoginResponse {
  success: boolean;
  message?: string;
  msg?: string;
  data?: any;
}

/**
 * 验证S-UI面板API连接
 * @param formData 配置表单数据
 * @param certFingerprints 可选的证书指纹数组
 * @returns Promise<{success: boolean, error?: string}> 返回验证结果和错误信息
 */
export async function validateSUIConnection(
  formData: ConfigFormData,
  publicKeyHashes?: string[]
): Promise<{success: boolean, error?: string}> {
  try {
    const { protocol, url, api } = formData;
    
    if (!url || !api) {
      throw new Error('URL and API key are required');
    }

    // 构建完整的API URL
    const baseUrl = `${protocol}://${url}`;
    const loginUrl = `${baseUrl}/login`;

    // 准备请求头
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');

    // 准备请求体 - S-UI使用API key进行验证
    const urlencoded = new URLSearchParams();
    urlencoded.append('api', api);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: urlencoded.toString(), // 转换为字符串
      redirect: 'follow'
    };

    // 如果有公钥哈希，初始化SSL固定
    if (publicKeyHashes && publicKeyHashes.length > 0) {
      const urlObj = new URL(loginUrl);
      await SSLPinningManager.initializePinning(urlObj.hostname, publicKeyHashes);
    }

    // 发送请求 - SSL固定会自动应用
    const response = await fetch(loginUrl, requestOptions);
    
    // 检查HTTP状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 解析响应
    let result: SUILoginResponse;
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      result = await response.json();
    } else {
      // 如果不是JSON响应，尝试解析文本
      const text = await response.text();
      try {
        result = JSON.parse(text);
      } catch {
        // 如果无法解析为JSON，验证失败
        throw new Error('Invalid response format: expected JSON');
      }
    }

    // 检查业务逻辑成功标志
    if (result.success === true) {
      return { success: true };
    } else {
      // 如果success不为true，返回API响应中的message
      const errorMessage = result.message || result.msg || 'Login failed';
      return { success: false, error: errorMessage };
    }

  } catch (error) {
    console.error('S-UI connection validation failed:', error);

    // 提供更详细的错误信息
    let errorMessage = 'Connection failed';

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Fetch error details:', {
        message: error.message,
        stack: error.stack,
        formData: { url: formData.url, protocol: formData.protocol },
        publicKeyHashes: publicKeyHashes?.length || 0
      });
    }

    return { success: false, error: errorMessage };
  }
}

/**
 * 构建S-UI面板的完整配置URL
 * @param protocol 协议类型
 * @param url 基础URL
 * @returns 完整的面板URL
 */
export function buildSUIUrl(protocol: string, url: string): string {
  return `${protocol}://${url}`;
}

/**
 * 验证S-UI配置表单数据
 * @param formData 表单数据
 * @returns 验证结果和错误信息
 */
export function validateSUIFormData(formData: ConfigFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!formData.name?.trim()) {
    errors.push('Configuration name is required');
  }

  if (!formData.url?.trim()) {
    errors.push('URL is required');
  } else {
    try {
      // 验证URL格式
      new URL(`${formData.protocol}://${formData.url}`);
    } catch {
      errors.push('Invalid URL format');
    }
  }

  if (!formData.api?.trim()) {
    errors.push('API key is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



/**
 * 获取S-UI服务器状态
 * @param config S-UI配置
 * @returns Promise<ServerStatus | null> 返回服务器状态或null
 */
export async function getSUIServerStatus(config: SUIConfig): Promise<ServerStatus | null> {
  try {
    const store = useAppStore.getState();

    const baseUrl = `${config.protocol}://${config.url}`;
    const statusUrl = `${baseUrl}/api/status?r=cpu,mem,net,sys,sbd`;

    // 获取或创建认证信息
    let authInfo = store.getAuthInfo(config.id);

    // 如果有证书指纹且尚未固定，先进行SSL固定（在API调用之前确保安全）
    if (config.certFingerprints && config.certFingerprints.length > 0 && (!authInfo || !authInfo.isPinned)) {
      const urlObj = new URL(statusUrl);
      const pinned = await SSLPinningManager.initializePinning(urlObj.hostname, config.certFingerprints);
      if (pinned) {
        // 如果没有authInfo，创建一个基本的
        if (!authInfo) {
          const expiresAt = new Date();
          expiresAt.setMinutes(expiresAt.getMinutes() + 30);
          authInfo = {
            expiresAt: expiresAt.toISOString(),
            isPinned: true
          };
        } else {
          // 更新现有authInfo中的固定状态
          authInfo.isPinned = true;
        }
        store.setAuthInfo(config.id, authInfo);
      }
    }

    // 准备请求头 - S-UI使用API key进行认证
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authorization', `Bearer ${config.api}`);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers
    };

    // 发送请求
    const response = await fetch(statusUrl, requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success && result.obj) {
      return result.obj as ServerStatus;
    }

    return null;
  } catch (error) {
    console.error('S-UI get server status failed:', error);
    return null;
  }
}

/**
 * 重启S-UI Sing-box服务
 * @param config S-UI配置
 * @returns Promise<{success: boolean, error?: string}> 返回重启结果
 */
export async function restartSUIService(config: SUIConfig): Promise<{success: boolean, error?: string}> {
  try {
    const baseUrl = `${config.protocol}://${config.url}`;
    const restartUrl = `${baseUrl}/api/restartSb`;

    // 准备请求头 - S-UI使用API key进行认证
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authorization', `Bearer ${config.api}`);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers
    };

    // 发送请求
    const response = await fetch(restartUrl, requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success) {
      return { success: true };
    } else {
      return { success: false, error: result.msg || 'Restart failed' };
    }
  } catch (error) {
    console.error('S-UI restart service failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
