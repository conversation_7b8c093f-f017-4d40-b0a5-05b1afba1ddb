import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { getUsageColor, monitorCardStyles } from '~/components/styles/MonitorCardStyles';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig, MonitoringStatus } from '@/lib/types';
import { getSUIServerStatus } from '@/panels/s-ui/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { RefreshCw, ArrowUp, ArrowDown } from 'lucide-react-native';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import { Pie, PolarChart } from 'victory-native';

// 动画饼图组件
const AnimatedPieChart = ({ data, size = 90 }: { data: any[]; size?: number }) => {
  return (
    <PolarChart data={data} labelKey="label" valueKey="value" colorKey="color">
      <Pie.Chart innerRadius={30}>
        {({ slice }) => (
          <Pie.Slice animate={{ type: 'timing', duration: 300 }} />
        )}
      </Pie.Chart>
    </PolarChart>
  );
};

export default function SUIOverviewScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs, getMonitoringStatus, setMonitoringStatus } = useAppStore();

  // 直接从configs中计算config，无需useState和useEffect
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  // 数据获取相关状态
  const intervalRef = useRef<any>(null);
  const lastUpdateTimeRef = useRef<number>(0);

  // 检查配置有效性
  useEffect(() => {
    if (!configId) {
      Alert.alert(t('common.error'), t('sui.overview.configNotFound'));
      router.back();
      return;
    }

    if (!config) {
      Alert.alert(t('common.error'), t('sui.overview.configNotFound'));
      router.back();
      return;
    }
  }, [configId, config]);

  const status = config ? getMonitoringStatus(config.id) : null;

  // 获取服务器状态
  const fetchServerStatus = async () => {
    if (!config) return;

    const requestTime = Date.now();
    
    try {
      const serverStatus = await getSUIServerStatus(config);
      
      // 防止过期响应覆盖新数据
      if (requestTime < lastUpdateTimeRef.current) {
        return;
      }
      
      lastUpdateTimeRef.current = requestTime;
      
      const newStatus: MonitoringStatus = {
        isOnline: serverStatus !== null,
        lastUpdate: Date.now(),
        serverStatus: serverStatus || undefined,
        failureCount: serverStatus ? 0 : (status?.failureCount || 0) + 1,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to fetch S-UI server status:', error);
      
      // 防止过期响应覆盖新数据
      if (requestTime < lastUpdateTimeRef.current) {
        return;
      }
      
      lastUpdateTimeRef.current = requestTime;
      
      const newStatus: MonitoringStatus = {
        isOnline: false,
        lastUpdate: Date.now(),
        serverStatus: undefined,
        failureCount: (status?.failureCount || 0) + 1,
      };

      setMonitoringStatus(config.id, newStatus);
    }
  };

  // 启动定时器
  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // 立即获取一次数据
    fetchServerStatus();
    
    // 设置定时器
    intervalRef.current = setInterval(fetchServerStatus, 3000);
  }, [config]);

  // 停止定时器
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 页面聚焦时启动定时器
  useFocusEffect(
    useCallback(() => {
      startPolling();
      return () => stopPolling();
    }, [startPolling, stopPolling])
  );

  // 手动刷新
  const handleRefresh = useCallback(() => {
    fetchServerStatus();
  }, [config]);

  // 安全数字转换
  const safeNumber = (value: any): number => {
    if (typeof value === 'number' && !isNaN(value)) return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  };

  // 格式化字节
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };

  // 格式化运行时间
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // 获取主机名
  const getHostname = (): string => {
    if (!config?.url) return '';
    try {
      return new URL(`${config.protocol}://${config.url}`).hostname;
    } catch {
      return config.url.split('/')[0];
    }
  };

  // 兼容不同s-ui返回结构的辅助变量
  const anyStatus = status?.serverStatus as any | undefined;
  const cpuCoresDisplay = status?.serverStatus ? safeNumber(anyStatus?.sys?.cpuCount ?? (status.serverStatus as any).cpuCores ?? 0) : 0;
  const netUpSpeed = status?.serverStatus ? safeNumber(anyStatus?.net?.psent ?? anyStatus?.netIO?.up ?? 0) : 0;
  const netUpTotal = status?.serverStatus ? safeNumber(anyStatus?.net?.sent ?? anyStatus?.netTraffic?.sent ?? 0) : 0;
  const netDownSpeed = status?.serverStatus ? safeNumber(anyStatus?.net?.precv ?? anyStatus?.netIO?.down ?? 0) : 0;
  const netDownTotal = status?.serverStatus ? safeNumber(anyStatus?.net?.recv ?? anyStatus?.netTraffic?.recv ?? 0) : 0;

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: textColor }]}>
            {t('sui.overview.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        {!status || !status.isOnline ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: textColor + '60' }]}>
              No Data
            </Text>
            <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
              {status?.isOnline === false
                ? t('sui.overview.serverOffline')
                : t('sui.overview.gettingData')}
            </Text>
          </View>
        ) : (
          <>
            {/* S-UI 状态卡片 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  {t('sui.overview.serverStatus')}
                </Text>
                <TouchableOpacity
                  onPress={handleRefresh}
                  style={[styles.actionButton, { borderColor }]}
                >
                  <RefreshCw size={16} color={textColor} />
                  <Text style={[styles.actionButtonText, { color: textColor }]}>
                    {t('common.refresh')}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={[styles.serverCard, { backgroundColor, borderColor }]}>
                {/* 服务器信息 */}
                <View style={styles.serverInfo}>
                  <Text style={[styles.configName, { color: textColor }]}>{config.name}</Text>
                  <Text style={[styles.configUrl, { color: textColor + '80' }]}>{getHostname()}</Text>
                </View>

                {/* 状态徽章 */}
                <View style={styles.statusContainer}>
                  <Badge
                    variant={status.isOnline ? "default" : "destructive"}
                    style={styles.statusBadge}
                  >
                    <Text style={styles.badgeText}>
                      {status.isOnline
                        ? status.serverStatus ? formatUptime(status.serverStatus.uptime) : 'Online'
                        : 'Offline'
                      }
                    </Text>
                  </Badge>
                </View>
              </View>
            </View>

            {/* 系统资源监控 */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                {t('sui.overview.systemResources')}
              </Text>
              
              <View style={[styles.resourceCard, { backgroundColor, borderColor }]}>
                {/* 系统资源使用率圆环图 */}
                <View style={monitorCardStyles.chartsContainer}>
                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus ? (() => {
                          const cpu = safeNumber(status.serverStatus.cpu);
                          const cpuPercentage = cpu <= 1 ? cpu * 100 : cpu;
                          return [
                            {
                              label: 'Used',
                              value: cpuPercentage,
                              color: getUsageColor(cpuPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - cpuPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                        size={90}
                      />
                      <View style={monitorCardStyles.chartInnerContent}>
                        <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>CPU</Text>
                        <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                          {status.serverStatus ? (() => {
                            const cpu = safeNumber(status.serverStatus.cpu);
                            const cpuPercentage = cpu <= 1 ? cpu * 100 : cpu;
                            return `${cpuPercentage.toFixed(1)}%`;
                          })() : 'No data'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                      {status.serverStatus ? `${cpuCoresDisplay} cores` : 'No data'}
                    </Text>
                  </View>

                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus ? (() => {
                          const memCurrent = safeNumber(status.serverStatus.mem.current);
                          const memTotal = safeNumber(status.serverStatus.mem.total);
                          const memPercentage = memTotal > 0 ? (memCurrent / memTotal) * 100 : 0;
                          return [
                            {
                              label: 'Used',
                              value: memPercentage,
                              color: getUsageColor(memPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - memPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                        size={90}
                      />
                      <View style={monitorCardStyles.chartInnerContent}>
                        <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>Memory</Text>
                        <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                          {status.serverStatus ? (() => {
                            const memCurrent = safeNumber(status.serverStatus.mem.current);
                            const memTotal = safeNumber(status.serverStatus.mem.total);
                            const memPercentage = memTotal > 0 ? (memCurrent / memTotal) * 100 : 0;
                            return `${memPercentage.toFixed(1)}%`;
                          })() : 'No data'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                      {status.serverStatus ? `${formatBytes(safeNumber(status.serverStatus.mem.current))}/${formatBytes(safeNumber(status.serverStatus.mem.total))}` : 'No data'}
                    </Text>
                  </View>

                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus?.disk ? (() => {
                          const diskCurrent = safeNumber(status.serverStatus.disk.current);
                          const diskTotal = safeNumber(status.serverStatus.disk.total);
                          const diskPercentage = diskTotal > 0 ? (diskCurrent / diskTotal) * 100 : 0;
                          return [
                            {
                              label: 'Used',
                              value: diskPercentage,
                              color: getUsageColor(diskPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - diskPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                        size={90}
                      />
                      <View style={monitorCardStyles.chartInnerContent}>
                        <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>Storage</Text>
                        <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                          {status.serverStatus?.disk ? (() => {
                            const diskCurrent = safeNumber(status.serverStatus.disk.current);
                            const diskTotal = safeNumber(status.serverStatus.disk.total);
                            const diskPercentage = diskTotal > 0 ? (diskCurrent / diskTotal) * 100 : 0;
                            return `${diskPercentage.toFixed(1)}%`;
                          })() : 'No data'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                      {status.serverStatus?.disk ? `${formatBytes(safeNumber(status.serverStatus.disk.current))}/${formatBytes(safeNumber(status.serverStatus.disk.total))}` : 'No data'}
                    </Text>
                  </View>
                </View>

                {/* 网络流量 */}
                <View style={monitorCardStyles.networkContainer}>
                  <View style={monitorCardStyles.networkItem}>
                    <ArrowUp size={16} color="#10b981" />
                    <Text style={[monitorCardStyles.networkSpeed, { color: textColor }]}>
                      {formatSpeed(netUpSpeed)}
                    </Text>
                    <Text style={[monitorCardStyles.networkTotal, { color: textColor + '80' }]}>
                      {formatBytes(netUpTotal)}
                    </Text>
                  </View>
                  <View style={monitorCardStyles.networkItem}>
                    <ArrowDown size={16} color="#3b82f6" />
                    <Text style={[monitorCardStyles.networkSpeed, { color: textColor }]}>
                      {formatSpeed(netDownSpeed)}
                    </Text>
                    <Text style={[monitorCardStyles.networkTotal, { color: textColor + '80' }]}>
                      {formatBytes(netDownTotal)}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 32,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  serverCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  serverInfo: {
    flex: 1,
  },
  configName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  configUrl: {
    fontSize: 14,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  resourceCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
});
